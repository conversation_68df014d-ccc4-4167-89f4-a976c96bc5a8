document.addEventListener("DOMContentLoaded", () => {
  // --- Three.js Setup ---
  let scene,
    camera,
    renderer,
    stars,
    celestialObjects = []; // Array for planets/nebulae
  const starVertices = [];
  const textureLoader = new THREE.TextureLoader(); // Use one loader

  function initThreeJS() {
    const container = document.getElementById("canvas-container");
    if (!container) {
      console.error("Canvas container not found!");
      return;
    }
    scene = new THREE.Scene();
    camera = new THREE.PerspectiveCamera(
      60,
      window.innerWidth / window.innerHeight,
      1,
      2000
    ); // Increased far plane
    camera.position.z = 5;
    renderer = new THREE.WebGLRenderer({ alpha: true, antialias: true });
    renderer.setSize(window.innerWidth, window.innerHeight);
    renderer.setPixelRatio(window.devicePixelRatio);
    container.appendChild(renderer.domElement);

    // --- Create Stars (Same as before) ---
    const starCount = 7000;
    const starDistance = 800; // Slightly increased range
    for (let i = 0; i < starCount; i++) {
      const x = THREE.MathUtils.randFloatSpread(starDistance * 2);
      const y = THREE.MathUtils.randFloatSpread(starDistance * 1.5); // Keep vertical spread reasonable
      const z = THREE.MathUtils.randFloat(
        -starDistance * 1.5,
        starDistance / 2
      );
      starVertices.push(x, y, z);
    }
    const starsGeometry = new THREE.BufferGeometry();
    starsGeometry.setAttribute(
      "position",
      new THREE.Float32BufferAttribute(starVertices, 3)
    );
    const starTexture = textureLoader.load(
      "https://threejs.org/examples/textures/sprites/disc.png"
    );
    const starsMaterial = new THREE.PointsMaterial({
      color: 0xffffff,
      size: 1.5,
      sizeAttenuation: true,
      map: starTexture,
      alphaTest: 0.5,
      transparent: true,
      blending: THREE.AdditiveBlending,
    });
    stars = new THREE.Points(starsGeometry, starsMaterial);
    scene.add(stars);

    // --- MODIFICATION START: Create Celestial Objects (Planets/Nebulae) ---
    const numPlanets = 3; // How many larger objects
    const planetDistance = 1000; // Place them further out generally

    // Example Textures (Find or create your own PNGs with transparency)
    const planetTextures = [
      "https://via.placeholder.com/256/FF5733/000000?text=Planet1", // Placeholder: Orange-ish
      "https://via.placeholder.com/256/337AFF/000000?text=Planet2", // Placeholder: Blue-ish
      "https://via.placeholder.com/512x256/8833FF/000000?text=Nebula1", // Placeholder: Purple Nebula (use plane)
      // Add real texture URLs here
    ];

    for (let i = 0; i < numPlanets; i++) {
      let geometry, material, object;
      const textureUrl = planetTextures[i % planetTextures.length]; // Cycle through textures
      const texture = textureLoader.load(textureUrl);

      // Simple check if it looks like a nebula texture based on placeholder text
      const isNebula = textureUrl.includes("Nebula");

      if (isNebula) {
        // Create a Plane for Nebulae
        geometry = new THREE.PlaneGeometry(200, 100); // Large plane size
        material = new THREE.MeshBasicMaterial({
          map: texture,
          transparent: true,
          blending: THREE.AdditiveBlending, // Make it glowy
          side: THREE.DoubleSide, // Visible from both sides
          depthWrite: false, // Helps with transparency sorting issues
        });
      } else {
        // Create a Sphere for Planets
        geometry = new THREE.SphereGeometry(
          THREE.MathUtils.randFloat(20, 50),
          32,
          16
        ); // Random size
        material = new THREE.MeshBasicMaterial({
          // Basic material is fine for background
          map: texture,
          transparent: true,
          opacity: 0.85, // Slightly transparent
        });
      }

      object = new THREE.Mesh(geometry, material);

      // Position them far away and spread out
      object.position.x = THREE.MathUtils.randFloatSpread(planetDistance * 2.5);
      object.position.y = THREE.MathUtils.randFloatSpread(planetDistance * 1.5);
      object.position.z = THREE.MathUtils.randFloat(
        -planetDistance * 1.2,
        -planetDistance * 0.5
      ); // Generally behind stars

      // Give them a slow random rotation speed
      object.userData.rotationSpeed = {
        x: THREE.MathUtils.randFloat(-0.0002, 0.0002),
        y: THREE.MathUtils.randFloat(-0.0002, 0.0002),
      };
      // Give them a very slow random drift speed
      object.userData.driftSpeed = {
        x: THREE.MathUtils.randFloat(-0.05, 0.05),
        y: THREE.MathUtils.randFloat(-0.05, 0.05),
      };

      scene.add(object);
      celestialObjects.push(object);
    }
    // --- MODIFICATION END ---

    window.addEventListener("resize", onWindowResize, false);
    animateThreeJS();
  }

  function onWindowResize() {
    camera.aspect = window.innerWidth / window.innerHeight;
    camera.updateProjectionMatrix();
    renderer.setSize(window.innerWidth, window.innerHeight);
    renderer.setPixelRatio(window.devicePixelRatio);
  }

  function animateThreeJS() {
    requestAnimationFrame(animateThreeJS);

    // Animate Stars (Mostly same as before)
    if (stars) {
      const positions = stars.geometry.attributes.position.array;
      const count = positions.length;
      const speedFactor = 0.5;
      const yBoundary = 400; // Adjusted boundary for wider starfield view
      const xBoundary = yBoundary * camera.aspect; // Use camera aspect ratio

      for (let i = 0; i < count; i += 3) {
        positions[i + 1] -= speedFactor * (0.5 + Math.random() * 0.5); // Y
        positions[i] -= speedFactor * (0.1 + Math.random() * 0.2); // X drift

        // Reset star if it goes below boundary OR too far left/right
        if (
          positions[i + 1] < -yBoundary ||
          Math.abs(positions[i]) > xBoundary * 1.2
        ) {
          positions[i + 1] = yBoundary * (0.8 + Math.random() * 0.4); // Reset Y to top
          positions[i] = THREE.MathUtils.randFloatSpread(xBoundary * 2); // Reset X randomly
          positions[i + 2] = THREE.MathUtils.randFloat(-800, 100); // Reset depth
        }
      }
      stars.geometry.attributes.position.needsUpdate = true;
      // stars.rotation.y += 0.00005; // Keep subtle rotation if liked
    }

    // --- MODIFICATION START: Animate Celestial Objects ---
    const celestialResetY = 800; // Reset boundary far out
    const celestialResetX = celestialResetY * camera.aspect;

    celestialObjects.forEach((obj) => {
      // Apply slow drift
      obj.position.x += obj.userData.driftSpeed.x;
      obj.position.y += obj.userData.driftSpeed.y;

      // Apply slow rotation
      obj.rotation.x += obj.userData.rotationSpeed.x;
      obj.rotation.y += obj.userData.rotationSpeed.y;

      // Simple boundary check and reset (less frequent than stars)
      if (
        obj.position.y < -celestialResetY ||
        Math.abs(obj.position.x) > celestialResetX * 1.5
      ) {
        obj.position.y = celestialResetY * (0.8 + Math.random() * 0.2);
        obj.position.x = THREE.MathUtils.randFloatSpread(celestialResetX * 2);
        obj.position.z = THREE.MathUtils.randFloat(-1000, -500); // Reset depth far back
      }
    });
    // --- MODIFICATION END ---

    renderer.render(scene, camera);
  }

  // --- Intersection Observer for Scroll Transitions ---
  const sectionsToObserve = document.querySelectorAll(".scroll-observe");

  const observerOptions = {
    root: null, // Use the viewport as the root
    rootMargin: "0px",
    threshold: 0.1, // Trigger when 10% of the element is visible
  };

  const observerCallback = (entries, observer) => {
    entries.forEach((entry) => {
      if (entry.isIntersecting) {
        entry.target.classList.add("is-visible");
      } else {
        entry.target.classList.remove("is-visible");
      }
    });
  };

  const intersectionObserver = new IntersectionObserver(
    observerCallback,
    observerOptions
  );

  sectionsToObserve.forEach((section) => {
    intersectionObserver.observe(section);
  });

  // --- Initialize Three.js ---
  initThreeJS();

  // --- Anime.js Animations (Keep Hero animation) ---
  var textWrapper = document.querySelector(".ml1 .letters");
  if (textWrapper) {
    textWrapper.innerHTML = textWrapper.textContent.replace(
      /\S/g,
      "<span class='letter'>$&</span>"
    );
    anime
      .timeline({ loop: false })
      .add({
        targets: ".ml1 .line",
        scaleX: [0, 1],
        opacity: [0.5, 1],
        easing: "easeInOutExpo",
        duration: 900,
      })
      .add({
        targets: ".ml1 .letter",
        opacity: [0, 1],
        translateX: [40, 0],
        translateZ: 0,
        scaleX: [0.3, 1],
        easing: "easeOutExpo",
        duration: 800,
        offset: "-=600",
        delay: (el, i) => 150 + 25 * i,
      })
      .add({
        targets: ".ml1",
        opacity: [1, 0.7, 1],
        scale: [1, 1.02, 1],
        duration: 2500,
        easing: "easeInOutSine",
        loop: true,
        direction: "alternate",
        delay: 1000,
      });
  }
  anime({
    targets: ".animate-fade-in",
    opacity: [0, 1],
    translateY: [20, 0],
    delay: anime.stagger(200, { start: 1200 }),
    duration: 1000,
    easing: "easeOutExpo",
  });

  // --- CV Modal Functionality ---
  const cvButton = document.getElementById("cv-button");
  const cvModal = document.getElementById("cv-modal");
  const closeModal = document.querySelector(".close-modal");

  // Open modal when CV button is clicked
  cvButton.addEventListener("click", () => {
    cvModal.classList.add("show");
    document.body.style.overflow = "hidden"; // Prevent scrolling when modal is open
  });

  // Close modal when X is clicked
  closeModal.addEventListener("click", () => {
    cvModal.classList.remove("show");
    document.body.style.overflow = ""; // Re-enable scrolling
  });

  // Close modal when clicking outside the modal content
  cvModal.addEventListener("click", (e) => {
    if (e.target === cvModal) {
      cvModal.classList.remove("show");
      document.body.style.overflow = "";
    }
  });

  // Close modal with Escape key
  document.addEventListener("keydown", (e) => {
    if (e.key === "Escape" && cvModal.classList.contains("show")) {
      cvModal.classList.remove("show");
      document.body.style.overflow = "";
    }
  });
}); // End DOMContentLoaded
