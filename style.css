/* Basic Reset & Root Variables */
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

:root {
  --bg-color: #121212; /* Very dark grey */
  --surface-color: #1e1e1e; /* Slightly lighter dark grey */
  --primary-color: #bb86fc; /* Purple accent */
  --secondary-color: #03dac6; /* Teal accent */
  --text-color: #e0e0e0; /* Light grey text */
  --text-color-darker: #a0a0a0; /* Darker grey text */
  --font-primary: "Poppins", sans-serif;
  --transition-speed: 0.6s; /* Transition duration */
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-primary);
  background-color: var(--bg-color);
  color: var(--text-color);
  line-height: 1.6;
  overflow-x: hidden; /* Prevent horizontal scroll */
}

/* Three.js Canvas Styling */
#canvas-container {
  position: fixed; /* Fixed position to stay in background */
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh; /* Full viewport height */
  z-index: -1; /* Place it behind other content */
  overflow: hidden;
}

/* Content Wrapper */
.content-wrapper {
  position: relative;
  z-index: 1;
  width: 100%;
  background-color: transparent;
}

/* General Section Styling */
.content-section {
  padding: 80px 5%;
  max-width: 1100px;
  margin: 0 auto 60px auto; /* Added bottom margin */
  background-color: rgba(30, 30, 30, 0.85);
  border-radius: 8px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.4);
  position: relative;
  z-index: 1;
}

/* #-------------------------------------- */
/*// Glow Border Animation //*/

.animated-border-box,
.animated-border-box-glow {
  min-height: 100%;
  max-width: auto;
  height: 100%;
  width: 100%;
  position: absolute;
  overflow: hidden;
  z-index: 0;
  border-radius: 10px;
  top: 0;
  left: 0;
}

.about-content {
  position: relative;
  z-index: 1;
  padding: 30px;
  height: 100%;
}

.animated-border-box-glow {
  overflow: hidden;
  /* Glow Blur */
  filter: blur(20px);
}

.animated-border-box:before,
.animated-border-box-glow:before {
  content: "";
  z-index: -2;
  text-align: center;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(0deg);
  position: absolute;
  width: 99999px;
  height: 99999px;
  background-repeat: no-repeat;
  background-position: 0 0;
  /*border color, change middle color*/
  background-image: conic-gradient(
    rgba(0, 0, 0, 0),
    #1976ed,
    rgba(0, 0, 0, 0) 25%
  );
  /* change speed here */
  animation: rotate 4s linear infinite;
}

.animated-border-box:after {
  content: "";
  position: absolute;
  z-index: -1;
  /* border width */
  left: 5px;
  top: 5px;
  /* double the px from the border width left */
  width: calc(100% - 10px);
  height: calc(100% - 10px);
  /*bg color*/
  background: #292a2e;
  /*box border radius*/
  border-radius: 7px;
}

@keyframes rotate {
  100% {
    transform: translate(-50%, -50%) rotate(1turn);
  }
}

/*// Border Animation END//*/

/*// Ignore This //*/

.center-box {
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #1d1e22;
}

/* #-------------------------------------- */

/* Ensure content wrapper has proper spacing */
.content-wrapper {
  position: relative;
  z-index: 1;
  width: 100%;
  background-color: transparent;
  padding-bottom: 40px; /* Added padding to bottom */
}

/* --- Scroll Transition Styling START --- */
.scroll-observe {
  opacity: 0;
  transform: translateY(50px); /* Start slightly lower */
  transition: opacity var(--transition-speed) ease-out,
    transform var(--transition-speed) ease-out;
  will-change: opacity, transform; /* Hint browser for performance */
}

.scroll-observe.is-visible {
  opacity: 1;
  transform: translateY(0); /* End at original position */
}
/* --- Scroll Transition Styling END --- */

/* Hero Section Specifics (No changes needed) */
.hero-section {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  padding: 40px 5%;
  position: relative;
  background: transparent;
  box-shadow: none;
  margin-bottom: 60px; /* Keep consistent margin */
}
/* Other hero styles remain the same */
.hero-content {
  max-width: 800px;
}
.ml1 .letters {
  display: inline-block;
  line-height: 1em;
  opacity: 1;
}
.ml1 .line {
  opacity: 0;
  position: absolute;
  left: 0;
  height: 3px;
  width: 100%;
  background-color: var(--primary-color);
  transform-origin: 0 0;
}
.ml1 .line1 {
  top: 0;
}
.ml1 .line2 {
  bottom: 0;
}
h1 {
  font-size: clamp(2.5rem, 6vw, 4.5rem);
  color: var(--primary-color);
  margin-bottom: 15px;
  font-weight: 700;
  position: relative;
  display: inline-block;
  padding: 10px 0;
}
.subtitle {
  font-size: clamp(1rem, 3vw, 1.5rem);
  color: var(--secondary-color);
  margin-bottom: 30px;
  font-weight: 300;
  opacity: 0;
}
.cta-button {
  display: inline-block;
  background-color: var(--primary-color);
  color: var(--bg-color);
  padding: 12px 30px;
  border-radius: 25px;
  text-decoration: none;
  font-weight: 600;
  transition: transform 0.3s ease, background-color 0.3s ease;
  border: none;
  cursor: pointer;
}
.cta-button:hover {
  background-color: var(--secondary-color);
  transform: translateY(-3px) scale(1.05);
}
.animate-fade-in {
  opacity: 0;
  transform: translateY(20px);
} /* Initial state for hero subtitle/button */

/* Sections Titles */
h2 {
  font-size: 2.5rem;
  color: var(--primary-color);
  margin-bottom: 30px;
  text-align: center;
  font-weight: 600;
}

/* Project Grid (No changes needed) */
.project-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
  margin-top: 30px;
}

.project-card {
  background-color: var(--surface-color);
  border-radius: 8px;
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.project-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(187, 134, 252, 0.3);
}

.project-card img {
  width: 100%;
  height: 200px;
  object-fit: cover;
  border-bottom: 2px solid rgba(187, 134, 252, 0.3);
}

.project-card h3 {
  color: var(--primary-color);
  padding: 15px 20px 5px;
  font-size: 1.4rem;
}

.project-card p {
  padding: 0 20px 15px;
  color: var(--text-color-darker);
}

/* Technology Icons for Projects */
.project-tech {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  padding: 0 20px 15px;
  margin-bottom: 10px;
}

.tech-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  background-color: rgba(30, 30, 30, 0.7);
  border-radius: 50%;
  transition: transform 0.2s ease, background-color 0.2s ease;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  cursor: pointer;
}

.tech-icon i {
  font-size: 18px;
  color: var(--secondary-color);
}

.tech-icon img {
  width: 20px;
  height: 20px;
  object-fit: contain;
  border: none;
}

.tech-icon:hover {
  transform: translateY(-3px) scale(1.1);
  background-color: rgba(3, 218, 198, 0.1);
}

.tech-icon:hover i {
  color: var(--primary-color);
}

.project-link {
  display: inline-block;
  margin: 0 20px 20px;
  padding: 8px 20px;
  background-color: var(--primary-color);
  color: var(--bg-color);
  border-radius: 20px;
  text-decoration: none;
  font-weight: 600;
  transition: background-color 0.3s ease, transform 0.3s ease;
}

.project-link:hover {
  background-color: var(--secondary-color);
  transform: translateY(-2px);
}

/* --- Contact Form Styling START --- */
#contact {
  text-align: center; /* Center heading and intro paragraph */
}

#contact-form {
  max-width: 600px; /* Limit form width */
  margin: 30px auto 0 auto; /* Center form horizontally */
  text-align: left; /* Align labels/inputs left */
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  color: var(--text-color-darker);
  font-weight: 400;
  font-size: 0.95rem;
}

.form-group input[type="text"],
.form-group input[type="email"],
.form-group textarea {
  width: 100%;
  padding: 12px 15px;
  border-radius: 5px;
  border: 1px solid #444; /* Darker border */
  background-color: #2a2a2a; /* Slightly lighter than surface */
  color: var(--text-color);
  font-size: 1rem;
  font-family: inherit;
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form-group input[type="text"]:focus,
.form-group input[type="email"]:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(187, 134, 252, 0.3); /* Subtle glow matching primary */
}

.form-group textarea {
  resize: vertical; /* Allow vertical resize */
  min-height: 120px;
}

.form-submit-button {
  display: block; /* Make button block level */
  width: auto; /* Adjust width automatically */
  margin: 20px auto 0 auto; /* Center button */
  opacity: 1; /* Ensure button is visible initially */
  transform: none; /* Override any lingering transform */
}

.contact-alternative {
  margin-top: 30px;
  font-size: 0.9rem;
  color: var(--text-color-darker);
}
.contact-alternative a {
  color: var(--secondary-color);
  text-decoration: none;
}
.contact-alternative a:hover {
  text-decoration: underline;
}

.social-icons {
  display: flex;
  justify-content: center;
  gap: 20px;
}

.social-icon a {
  color: var(--text-color);
  font-size: 24px;
  transition: color 0.3s ease;
}
.social-icons a i {
  color: var(--text-color);
  font-size: 24px;
  transition: color 0.3s ease;
}

/* --- Contact Form Styling END --- */

/* Footer */
.footer {
  text-align: center;
  padding: 20px;
  margin-top: 40px;
  color: var(--text-color-darker);
  font-size: 0.9rem;
  background-color: var(--surface-color);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  h1 {
    font-size: 2.5rem;
  }
  h2 {
    font-size: 2rem;
  }
  .content-section {
    padding: 60px 4%;
  }
  .project-grid {
    grid-template-columns: 1fr;
  }
}

/* --- Add or Modify these styles in your style.css --- */

/* ... (Keep all previous styles: Reset, Root Variables, Body, Canvas, Wrapper, Sections, Hero, Titles, Projects, Contact Form, Footer etc.) ... */

/* --- CV Download Button Styling --- */
.cv-download-button {
  position: absolute;
  top: 30px; /* Adjust vertical position */
  right: 30px; /* Adjust horizontal position */
  z-index: 10; /* Ensure it's above canvas/other elements */
  padding: 8px 20px; /* Slightly smaller padding */
  font-size: 0.9rem; /* Slightly smaller font */
  /* Inherits .cta-button styles like background, color, border-radius, transition */
  opacity: 1; /* Make sure it's visible */
  transform: none; /* Reset any transform inherited */
}

/* --- Tech Logo Strip Styling --- */

/* --- Modal Styles --- */
.modal {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  z-index: 100;
  overflow: auto;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.modal.show {
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 1;
}

.modal-content {
  background-color: var(--surface-color);
  margin: auto;
  width: 90%;
  max-width: 800px;
  border-radius: 8px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.5);
  animation: modalFadeIn 0.4s;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  max-height: 90vh;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.modal-header h2 {
  color: var(--primary-color);
  margin: 0;
  font-size: 1.5rem;
}

.close-modal {
  color: var(--text-color-darker);
  font-size: 28px;
  font-weight: bold;
  cursor: pointer;
  transition: color 0.2s ease;
}

.close-modal:hover {
  color: var(--primary-color);
}

.modal-body {
  padding: 20px;
  flex-grow: 1;
  overflow: auto;
}

.cv-container {
  width: 100%;
  height: 60vh;
  min-height: 400px;
  overflow: hidden;
  border-radius: 4px;
  background-color: rgba(0, 0, 0, 0.2);
}

#cv-iframe {
  width: 100%;
  height: 100%;
  border: none;
}

.modal-footer {
  padding: 15px 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  text-align: right;
}

.download-button {
  display: inline-block;
  margin: 0;
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* --- Responsive Adjustments --- */

@media (max-width: 768px) {
  /* ... (previous responsive styles) ... */

  .cv-download-button {
    top: 15px;
    right: 15px;
    padding: 6px 15px;
    font-size: 0.8rem;
  }

  .modal-content {
    width: 95%;
    max-height: 95vh;
  }

  .cv-container {
    height: 50vh;
  }

  .tech-strip-content i {
    font-size: 35px;
    margin: 0 15px;
  }
  /*
  .tech-strip-content img {
      height: 35px;
      margin: 0 15px;
  }
  */
  .tech-strip-content {
    animation-duration: 25s; /* Potentially speed up slightly on smaller screens */
  }
}

/* -------------------------Scrolling logos---------------------- */

/* Container to limit the scroll width */
.logo-container {
  /* Setting custom properties for logo dimensions, spacing, and animation duration */
  --logo-width: 200px; /* Width of each logo */
  --logo-height: 100px; /* Height of each logo */
  --gap: calc(
    var(--logo-width) / 14
  ); /* Space between logos, calculated based on logo width */
  --duration: 60s; /* Duration of the scroll animation */
  --scroll-start: 0; /* Start position of scroll animation */
  --scroll-end: calc(-100% - var(--gap)); /* End position of scroll animation */

  display: flex; /* Aligns children in a row */
  flex-direction: column; /* Stacks content vertically */
  gap: var(--gap); /* Adds space between child elements */
  margin: auto; /* Centers container horizontally */
  max-width: 100vw; /* Limits width to the viewport */
}

/* Scrolling area */
.logo-scroll {
  display: flex; /* Aligns logos in a horizontal row */
  overflow: hidden; /* Hides overflow to create infinite scroll effect */
  user-select: none; /* Disables text selection */
  gap: var(--gap); /* Space between logos */
  mask-image: linear-gradient(
    to right,
    hsl(0 0% 0% / 0),
    hsl(0 0% 0% / 1) 30%,
    hsl(0 0% 0% / 1) 70%,
    hsl(0 0% 0% / 0)
  ); /* Adds a gradient mask to fade edges */
  background-color: rgba(0, 0, 0, 0.2);
}

.logo-scroll__wrapper {
  flex-shrink: 0; /* Prevents wrapper from shrinking */
  display: flex; /* Aligns logos horizontally */
  align-items: center; /* Centers logos vertically */
  justify-content: space-around; /* Distributes logos evenly */
  padding: 10px;
  gap: var(--gap); /* Adds spacing between logos */
  min-width: 100%; /* Wrapper width covers full viewport */
  animation: scroll var(--duration) linear infinite; /* Infinite scrolling animation */
}

.logo-scroll__wrapper:nth-child(even) {
  margin-left: calc(
    var(--logo-width) / -2
  ); /* Offsets even wrappers for smooth scroll overlap */
}

.logo-scroll__wrapper:hover {
  animation-play-state: paused; /* Pauses animation when wrapper is hovered */
}

/* Logo styling */
.logo-item {
  /* width: var(--logo-width); /* Sets width of each logo */
  /* height: var(--logo-height); Sets height of each logo */
  width: 50px;
  height: 50px;
  transition: transform 0.5s;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  border-radius: 4px; /* Rounds the logo corners /
}

.logo-scroll .logo-item:hover {
  transform: scale(1.05); /* Slightly enlarges logo when hovered */
}

/* Infinite scroll animation */
@keyframes scroll {
  from {
    transform: translateX(var(--scroll-start)); /* Animation start position */
  }
  to {
    transform: translateX(var(--scroll-end)); /* Animation end position */
  }
}
