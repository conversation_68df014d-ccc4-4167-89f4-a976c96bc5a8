<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Your Name - Portfolio</title>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;600;700&display=swap"
      rel="stylesheet"
    />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <link rel="stylesheet" href="style.css" />
    
  </head>
  <body>
 
    <!-- ---------------------------Hero section-------------------------  -->
    <div id="canvas-container"></div>

    <div class="content-wrapper">
      <section id="hero" class="hero-section">
        <button id="cv-button" class="cta-button cv-download-button">
          View CV
        </button>
        <div class="hero-content">
          <h1 class="ml1">
            <span class="text-wrapper">
              <span class="line line1"></span>
              <span class="letters">Hi, I'm Aditya </span>
              <span class="line line2"></span>
            </span>
          </h1>
          <p class="subtitle animate-fade-in">
            Creative Web Developer & Designer , AIML Enthusiast
          </p>
          
          <a href="#projects" class="cta-button animate-fade-in"
            >View My Work</a
          >
        </div>
      </section>
      <!-- ----------------------------About Section----------------------------------- -->

      <section id="about" class="content-section scroll-observe">
        <div class="animated-border-box-glow"></div>
        <div class="animated-border-box"></div>
        <div class="about-content">
          <h2>About Me</h2>
          <p>
            Hi, I'm Aditya Kapile . Welcome to my digital space! An electronics engineer who enjoys exploring the world of technology and building useful things. I'm passionate about learning new skills, especially in areas like web development and data science. Whether it's creating a simple app or working on a creative idea, I love the process of turning thoughts into something real. I'm always up for a new challenge and excited to keep growing through hands-on experience.
            
          </p>

<!-- ----------------------------------------------------- -->

     
      </section>

      <!-- -----------------Project Section ---------------------- -->

      <section id="projects" class="content-section scroll-observe">
        <div class="animated-border-box-glow"></div>
        <div class="animated-border-box"></div>
        <div class="about-content">
        <h2>Projects</h2>
        <div class="project-grid">
          <div class="project-card">
            <img
              src="assets\inventory-gif.gif"
              alt="Project 1 Screenshot"
            />
            <h3>Component Inventory Management System</h3>
            <p>
              This web-based application is designed to help the Electronics Department efficiently manage its inventory of electronic components. Built using Django (Python), the system tracks the components issued to students, monitors due dates, calculates fines for late returns, and maintains detailed records of all transactions. It includes role-based access for admins, staff, and students, offering functionalities such as component issuance, returns, stock updates, and real-time reports. The system improves accountability, saves manual effort, and ensures smooth inventory management within the department
            </p>
            <div class="project-tech">
              <div class="tech-icon" title="Python"><i class="fab fa-python"></i></div>
              <div class="tech-icon" title="Django"><img src="assets/django.svg" alt="Django"></div>
              <div class="tech-icon" title="HTML5"><i class="fab fa-html5"></i></div>
              <div class="tech-icon" title="CSS3"><i class="fab fa-css3-alt"></i></div>
              <div class="tech-icon" title="JavaScript"><i class="fab fa-js"></i></div>
            </div>
            <a href="https://github.com/pradipmasal/CoE.git" target="_blank" class="project-link">View Project</a>
          </div>
          <div class="project-card">
            <img
              src="assets\pdfChatter-gif.gif"
              alt="Project 2 Screenshot"
            />
            <h3>PDF Chatter – RAG-based AI Assistant</h3>
            <p>
              Built an interactive PDF chatbot using LangChain, Streamlit, and a Retrieval-Augmented Generation (RAG) pipeline. Users can upload PDF documents and engage in real-time conversations, with the system retrieving relevant context from the file to generate accurate and intelligent responses.
            </p>
            <div class="project-tech">
              <div class="tech-icon" title="Python"><i class="fab fa-python"></i></div>
              <div class="tech-icon" title="LangChain"><img src="assets/langchain.svg" alt="LangChain"></div>
              <div class="tech-icon" title="AI"><img src="assets/ai.svg" alt="AI"></div>
              <div class="tech-icon" title="Streamlit"><i class="fas fa-chart-line"></i></div>
            </div>
            <a href="#" target="_blank" class="project-link">View Project</a>
          </div>
          <div class="project-card">
            <img
              src="assets\extension.png"
              alt="Project 3 Screenshot"
            />
            <h3>Webpage AI Summarizer – Chrome Extension</h3>
            <p>
              Built a Chrome extension for summarizing and querying any webpage using AI — developed in a creative "vibe coding" session. The extension captures the active tab's URL, sends it to a FastAPI backend powered by LangChain and OpenAI, extracts readable content, and displays AI-generated summaries. Technologies: JavaScript, FastAPI, LangChain, OpenAI API, Chrome Extension APIs.
            </p>
            <div class="project-tech">
              <div class="tech-icon" title="JavaScript"><i class="fab fa-js"></i></div>
              <div class="tech-icon" title="FastAPI"><i class="fas fa-bolt"></i></div>
              <div class="tech-icon" title="LangChain"><img src="assets/langchain.svg" alt="LangChain"></div>
              <div class="tech-icon" title="OpenAI"><img src="assets/ai.svg" alt="OpenAI"></div>
              <div class="tech-icon" title="Chrome Extension"><i class="fab fa-chrome"></i></div>
            </div>
            <a href="#" target="_blank" class="project-link">View Project</a>
          </div>
        </div>
      </section>
      </div>
    </div>

    <!-- -------------------------Contact Section------------------------------- -->

    <section id="contact" class="content-section scroll-observe">
      <div class="animated-border-box-glow"></div>
      <div class="animated-border-box"></div>
      <div class="about-content">
      <h2>Get In Touch</h2>
      <p>
        Interested in working together or have a question? Fill out the form
        below or email me directly!
      </p>

      <form
        id="contact-form"
        action="https://formspree.io/f/YOUR_FORMSPREE_ID"
        method="POST"
      >
        
        <div class="form-group">
          <label for="name">Name:</label>
          <input type="text" id="name" name="name" required />
        </div>
        <div class="form-group">
          <label for="email">Email:</label>
          <input type="email" id="email" name="email" required />
        </div>
        <div class="form-group">
          <label for="subject">Subject:</label>
          <input type="text" id="subject" name="subject" />
        </div>
        <div class="form-group">
          <label for="message">Message:</label>
          <textarea id="message" name="message" rows="5" required></textarea>
        </div>
        <button type="submit" class="cta-button form-submit-button">
          Send Message
        </button>
      </form>
      <p class="contact-alternative">
        Or email me at:
        <a href="mailto:<EMAIL>"><EMAIL></a>
      </p>
      <div class="social-icons">
        <a href="http://www.linkedin.com/in/aditya-kapile" target="_blank" class="social-icon">
          <i class="fab fa-linkedin"></i>
        </a>
        <a href="https://github.com/aditya-kapile" target="_blank" class="social-icon">
          <i class="fab fa-github"></i>
        </a>
        <a href="https://www.instagram.com/your-instagram-profile" target="_blank" class="social-icon">
          <i class="fab fa-instagram"></i>
        </a>
        <a href="" target="_blank" class="social-icon">
          <i class="fab fa-twitter"></i>
        
      
      </div>
    </section>


    <!-- -----------------------------Footer------------------------ -->

    <footer class="footer">
      <p>&copy; 2025 Aditya Kapile. All rights reserved.</p>
    </footer>

    <!-- CV Modal -->
    <div id="cv-modal" class="modal">
      <div class="modal-content">
        <div class="modal-header">
          <h2>My CV</h2>
          <span class="close-modal">&times;</span>
        </div>
        <div class="modal-body">
          <!-- CV content will be loaded here -->
          <div class="cv-container">
            <iframe
              id="cv-iframe"
              src="assets/my_CV.pdf"
              frameborder="0"
            ></iframe>
          </div>
        </div>
        <div class="modal-footer">
          <a
            href="assets/my_CV.pdf"
            download="my_CV.pdf"
            class="cta-button download-button"
            >Download CV</a
          >
        </div>
      </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/animejs/3.2.1/anime.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>

    <script src="script.js"></script>
  </body>
</html>
